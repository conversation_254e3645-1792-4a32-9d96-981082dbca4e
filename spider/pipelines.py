# Define your item pipelines here
#
# Don't forget to add your pipeline to the ITEM_PIPELINES setting
# See: https://docs.scrapy.org/en/latest/topics/item-pipeline.html


# useful for handling different item types with a single interface
from itemadapter import ItemAdapter
import psycopg2


class A36KrPipeline:
    def open_spider(self, spider):
        self.conn = psycopg2.connect(
            host="localhost",
            dbname="investment",
            user="postgres",
            password="123456",
        )
        self.cur = self.conn.cursor()
        self.cur.execute(
            """
            CREATE TABLE IF NOT EXISTS a36kr_projects (
                id SERIAL PRIMARY KEY,
                project_id TEXT,
                project_name TEXT,
                project_url TEXT,
                project_logo TEXT,
                project_brief TEXT,
                industry_list TEXT[],
                financing_round TEXT,
                financing_date TEXT,
                financing_money TEXT,
                investor TEXT
            );
            """
        )
        self.conn.commit()

    def close_spider(self, spider):
        self.cur.close()
        self.conn.close()

    def process_item(self, item, spider):
        self.cur.execute(
            """
            INSERT INTO a36kr_projects (
                project_id, project_name, project_url, project_logo, project_brief,
                industry_list, financing_round, financing_date, financing_money, investor
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """,
            (
                item.get("project_id"),
                item.get("project_name"),
                item.get("project_url"),
                item.get("project_logo"),
                item.get("project_brief"),
                item.get("industry_list"),
                item.get("financing_round"),
                item.get("financing_date"),
                item.get("financing_money"),
                item.get("investor"),
            ),
        )
        self.conn.commit()
        return item
