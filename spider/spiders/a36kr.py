from collections.abc import Iterable
from typing import Any
import scrapy
from scrapy.http import Response
from spider.items import A36krItem
import json
from spider.settings import ua


class A36krSpider(scrapy.Spider):
    name = "36kr"
    allowed_domains = ["pitchhub.36kr.com"]
    base_url = "https://gateway.36kr.com/api/pms/project/financing/list"
    page_size = 20
    max_page = 10

    def start_requests(self) -> Iterable[Any]:
        headers = {
            "Content-Type": "application/json",
            "User-Agent": ua.random,
        }
        for page_no in range(1, self.max_page):
            body = {
                "partner_id": "web",
                "timestamp": int(__import__("time").time() * 1000),
                "partner_version": "1.0.0",
                "param": {
                    "pageNo": page_no,
                    "pageSize": "20",
                    "siteId": 1,
                    "platformId": 2,
                },
            }
            yield scrapy.Request(
                url=self.base_url,
                method="POST",
                headers=headers,
                body=json.dumps(body),
                callback=self.parse,
                dont_filter=True,
            )

    def parse(self, response: Response):
        try:
            data = json.loads(response.body)
        except Exception as e:
            print(e)
            return
        content = data.get("data", {})
        financing_list = content.get("financingList", [])
        for l in financing_list:
            item = A36krItem()
            item["project_id"] = l.get("projectId")
            item["project_name"] = l.get("projectName")
            item["project_url"] = (
                f"https://pitchhub.36kr.com/project/{l.get('projectId')}"
            )
            item["project_logo"] = l.get("projectLogo")
            item["project_brief"] = l.get("projectBrief")
            item["industry_list"] = l.get("industryList", [])
            item["financing_round"] = l.get("financingRoundRemark")
            item["financing_date"] = l.get("financingDate")
            item["financing_money"] = l.get("financingMoney")
            item["investor"] = l.get("investor")
            yield item
